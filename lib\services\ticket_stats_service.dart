import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/ticket_model.dart';
import '../models/movie_model.dart';
import '../models/showtime_model.dart';
import '../models/screen_model.dart';
import '../services/firebase_movie_service.dart';
import '../services/showtime_service.dart';
import '../services/screen_service.dart';

class TicketStatsService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseMovieService _movieService = FirebaseMovieService();
  final ShowtimeService _showtimeService = ShowtimeService();
  final ScreenService _screenService = ScreenService();

  // Get ticket statistics for all movies
  Future<List<MovieTicketStats>> getAllMovieTicketStats() async {
    try {
      // Get all active movies
      final movies = await _movieService.getMovies();
      final List<MovieTicketStats> statsList = [];

      for (final movie in movies) {
        final stats = await getMovieTicketStats(movie.id);
        if (stats != null) {
          statsList.add(stats);
        }
      }

      // Sort by movie title
      statsList.sort((a, b) => a.movieTitle.compareTo(b.movieTitle));
      return statsList;
    } catch (e) {
      throw Exception('Failed to get movie ticket stats: $e');
    }
  }

  // Get ticket statistics for a specific movie
  Future<MovieTicketStats?> getMovieTicketStats(int movieId) async {
    try {
      // Get movie details
      final movie = await _movieService.getMovieByMovieId(movieId);
      if (movie == null) return null;

      // Get all tickets for this movie
      final ticketsSnapshot = await _firestore
          .collection('tickets')
          .where('movieId', isEqualTo: movieId)
          .get();

      final tickets =
          ticketsSnapshot.docs.map((doc) => Ticket.fromFirestore(doc)).toList();

      // Calculate total available tickets for this movie
      final totalAvailableTickets =
          await _calculateTotalAvailableTickets(movieId);

      return MovieTicketStats.fromData(
        movieId: movieId,
        movieTitle: movie.title,
        moviePosterPath: movie.posterPath,
        totalAvailableTickets: totalAvailableTickets,
        tickets: tickets,
      );
    } catch (e) {
      throw Exception(
          'Failed to get movie ticket stats for movie $movieId: $e');
    }
  }

  // Calculate total available tickets for a movie based on showtimes and screens
  Future<int> _calculateTotalAvailableTickets(int movieId) async {
    try {
      // Get all showtimes for this movie
      final showtimes = await _showtimeService.getShowtimesByMovie(movieId);
      int totalTickets = 0;

      for (final showtime in showtimes) {
        // Get screen details to know total seats
        final screen = await _screenService.getScreenById(showtime.screenId);
        if (screen != null) {
          totalTickets += screen.totalSeats;
        }
      }

      return totalTickets;
    } catch (e) {
      // If we can't calculate, return 0
      return 0;
    }
  }

  // Get tickets for a specific movie
  Future<List<Ticket>> getMovieTickets(int movieId) async {
    try {
      final snapshot = await _firestore
          .collection('tickets')
          .where('movieId', isEqualTo: movieId)
          .orderBy('purchaseDate', descending: true)
          .get();

      return snapshot.docs.map((doc) => Ticket.fromFirestore(doc)).toList();
    } catch (e) {
      throw Exception('Failed to get movie tickets: $e');
    }
  }

  // Get tickets for a specific movie with user information
  Future<List<Map<String, dynamic>>> getMovieTicketsWithUserInfo(
      int movieId) async {
    try {
      final tickets = await getMovieTickets(movieId);
      final List<Map<String, dynamic>> ticketsWithUserInfo = [];

      for (final ticket in tickets) {
        // Get user information
        String userName = 'Unknown User';
        String userEmail = '';

        try {
          final userDoc =
              await _firestore.collection('users').doc(ticket.userId).get();

          if (userDoc.exists) {
            final userData = userDoc.data() as Map<String, dynamic>;
            userName =
                userData['displayName'] ?? userData['name'] ?? 'Unknown User';
            userEmail = userData['email'] ?? '';
          }
        } catch (e) {
          // If we can't get user info, use default
        }

        ticketsWithUserInfo.add({
          'ticket': ticket,
          'userName': userName,
          'userEmail': userEmail,
        });
      }

      return ticketsWithUserInfo;
    } catch (e) {
      throw Exception('Failed to get movie tickets with user info: $e');
    }
  }

  // Cancel a ticket
  Future<void> cancelTicket(String ticketId, {double? refundAmount}) async {
    try {
      await _firestore.collection('tickets').doc(ticketId).update({
        'status': TicketStatus.cancelled.name,
        'cancelledAt': Timestamp.now(),
        'refundAmount': refundAmount ?? 0.0,
      });
    } catch (e) {
      throw Exception('Failed to cancel ticket: $e');
    }
  }

  // Get overall statistics
  Future<Map<String, dynamic>> getOverallStats() async {
    try {
      final allStats = await getAllMovieTicketStats();

      int totalMovies = allStats.length;
      int totalAvailableTickets = allStats.fold(
          0, (total, stats) => total + stats.totalAvailableTickets);
      int totalSoldTickets =
          allStats.fold(0, (total, stats) => total + stats.soldTickets);
      int totalRemainingTickets =
          allStats.fold(0, (total, stats) => total + stats.remainingTickets);
      double totalRevenue =
          allStats.fold(0.0, (total, stats) => total + stats.totalRevenue);

      double overallOccupancyRate = totalAvailableTickets > 0
          ? (totalSoldTickets / totalAvailableTickets) * 100
          : 0.0;

      return {
        'totalMovies': totalMovies,
        'totalAvailableTickets': totalAvailableTickets,
        'totalSoldTickets': totalSoldTickets,
        'totalRemainingTickets': totalRemainingTickets,
        'totalRevenue': totalRevenue,
        'overallOccupancyRate': overallOccupancyRate,
      };
    } catch (e) {
      throw Exception('Failed to get overall stats: $e');
    }
  }

  // Stream for real-time updates
  Stream<List<MovieTicketStats>> getAllMovieTicketStatsStream() {
    return _firestore
        .collection('tickets')
        .snapshots()
        .asyncMap((_) => getAllMovieTicketStats());
  }
}
